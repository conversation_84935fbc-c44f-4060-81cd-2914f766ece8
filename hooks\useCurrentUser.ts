import { useUser } from '@clerk/nextjs';
import { useState, useEffect } from 'react';
import { User } from '@/types';
import { DatabaseService } from '@/lib/database';

export function useCurrentUser() {
  const { user: clerkUser, isLoaded, isSignedIn } = useUser();
  const [dbUser, setDbUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUser() {
      if (!isLoaded) return;
      
      if (!isSignedIn || !clerkUser) {
        setDbUser(null);
        setLoading(false);
        return;
      }

      try {
        // Try to get user from our database
        const user = await DatabaseService.getUserByClerkId(clerkUser.id);
        
        if (!user) {
          // Create user if doesn't exist (fallback in case webhook failed)
          const newUser = await DatabaseService.createUser({
            clerkId: clerkUser.id,
            email: clerkUser.emailAddresses[0]?.emailAddress || '',
            name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
            subscription: 'free',
            usage: {
              presentations: 0,
              imagesGenerated: 0,
              exportsThisMonth: 0,
            },
            preferences: {
              defaultTemplate: 'modern',
              autoSave: true,
            },
          });
          setDbUser(newUser);
        } else {
          setDbUser(user);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        setDbUser(null);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [clerkUser, isLoaded, isSignedIn]);

  return {
    user: dbUser,
    clerkUser,
    isLoaded,
    isSignedIn,
    loading,
  };
}
