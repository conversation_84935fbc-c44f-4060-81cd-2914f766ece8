import { NextRequest, NextResponse } from 'next/server';
import { ImageService } from '@/lib/image-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const count = parseInt(searchParams.get('count') || '10');
    const orientation = searchParams.get('orientation') as 'landscape' | 'portrait' | 'squarish' || 'landscape';

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: 'Search query is required' },
        { status: 400 }
      );
    }

    if (count < 1 || count > 30) {
      return NextResponse.json(
        { success: false, error: 'Count must be between 1 and 30' },
        { status: 400 }
      );
    }

    const images = await ImageService.searchImages(query.trim(), count, orientation);

    return NextResponse.json({
      success: true,
      images,
      query,
      count: images.length,
    });

  } catch (error) {
    console.error('Error in image search API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to search images' 
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
