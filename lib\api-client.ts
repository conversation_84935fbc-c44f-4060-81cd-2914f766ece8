import { GenerationRequest, GenerationResponse, Presentation } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

export class ApiClient {
  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/api${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Presentation Generation
  static async generatePresentation(request: GenerationRequest): Promise<GenerationResponse> {
    return this.request<GenerationResponse>('/presentations/generate', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // Presentation CRUD Operations
  static async createPresentation(presentation: Partial<Presentation>): Promise<{ success: boolean; presentation: Presentation }> {
    return this.request('/presentations', {
      method: 'POST',
      body: JSON.stringify(presentation),
    });
  }

  static async getPresentations(userId: string, limit = 20, skip = 0): Promise<{ success: boolean; presentations: Presentation[]; count: number }> {
    const params = new URLSearchParams({
      userId,
      limit: limit.toString(),
      skip: skip.toString(),
    });
    
    return this.request(`/presentations?${params}`);
  }

  static async getPresentation(id: string): Promise<{ success: boolean; presentation: Presentation }> {
    return this.request(`/presentations/${id}`);
  }

  static async updatePresentation(id: string, updates: Partial<Presentation>): Promise<{ success: boolean; presentation: Presentation }> {
    return this.request(`/presentations/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  static async deletePresentation(id: string): Promise<{ success: boolean; message: string }> {
    return this.request(`/presentations/${id}`, {
      method: 'DELETE',
    });
  }

  // Image Search (for Unsplash integration)
  static async searchImages(query: string, count = 10): Promise<{ success: boolean; images: any[] }> {
    const params = new URLSearchParams({
      query,
      count: count.toString(),
    });
    
    return this.request(`/images/search?${params}`);
  }

  // Template Operations
  static async getTemplates(): Promise<{ success: boolean; templates: any[] }> {
    return this.request('/templates');
  }

  static async getTemplate(id: string): Promise<{ success: boolean; template: any }> {
    return this.request(`/templates/${id}`);
  }

  // User Operations (for when we add user management)
  static async getUser(id: string): Promise<{ success: boolean; user: any }> {
    return this.request(`/users/${id}`);
  }

  static async updateUser(id: string, updates: any): Promise<{ success: boolean; user: any }> {
    return this.request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  // Export Operations
  static async exportPresentation(id: string, format: 'pdf' | 'pptx' | 'png'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/api/presentations/${id}/export?format=${format}`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // Utility method for handling file downloads
  static downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
