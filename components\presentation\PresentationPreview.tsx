'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import <PERSON><PERSON><PERSON>enderer from './SlideRenderer';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  PlayIcon,
  PauseIcon,
  ArrowsPointingOutIcon
} from '@heroicons/react/24/outline';
import { Presentation, Template } from '@/types';

interface PresentationPreviewProps {
  presentation: Presentation;
  template: Template;
  className?: string;
}

export default function PresentationPreview({ 
  presentation, 
  template, 
  className = '' 
}: PresentationPreviewProps) {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const currentSlide = presentation.slides[currentSlideIndex];
  const totalSlides = presentation.slides.length;

  const goToNextSlide = () => {
    if (currentSlideIndex < totalSlides - 1) {
      setCurrentSlideIndex(currentSlideIndex + 1);
    }
  };

  const goToPreviousSlide = () => {
    if (currentSlideIndex > 0) {
      setCurrentSlideIndex(currentSlideIndex - 1);
    }
  };

  const goToSlide = (index: number) => {
    if (index >= 0 && index < totalSlides) {
      setCurrentSlideIndex(index);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
    // TODO: Implement auto-advance functionality
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // TODO: Implement fullscreen mode
  };

  if (!currentSlide) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">No slides to preview</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{presentation.title}</CardTitle>
            <div className="flex items-center space-x-2 mt-1">
              <Badge variant="secondary">{template.name}</Badge>
              <span className="text-sm text-gray-500">
                Slide {currentSlideIndex + 1} of {totalSlides}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={togglePlayback}
              disabled
              title="Slideshow mode (coming soon)"
            >
              {isPlaying ? (
                <PauseIcon className="h-4 w-4" />
              ) : (
                <PlayIcon className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              disabled
              title="Fullscreen mode (coming soon)"
            >
              <ArrowsPointingOutIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {/* Slide Display */}
        <div className="relative bg-gray-100 p-4">
          <SlideRenderer
            slide={currentSlide}
            template={template}
            isPreview={true}
            className="mx-auto max-w-4xl"
          />
        </div>
        
        {/* Navigation Controls */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousSlide}
              disabled={currentSlideIndex === 0}
            >
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              Previous
            </Button>
            
            {/* Slide Thumbnails */}
            <div className="flex items-center space-x-1 max-w-md overflow-x-auto">
              {presentation.slides.map((slide, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`flex-shrink-0 w-12 h-8 rounded border-2 transition-colors ${
                    index === currentSlideIndex
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                  title={`Slide ${index + 1}: ${slide.title}`}
                >
                  <div className="w-full h-full rounded bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {index + 1}
                    </span>
                  </div>
                </button>
              ))}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextSlide}
              disabled={currentSlideIndex === totalSlides - 1}
            >
              Next
              <ChevronRightIcon className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
        
        {/* Slide Info */}
        <div className="p-4 border-t">
          <div className="text-sm text-gray-600">
            <div className="font-medium mb-1">{currentSlide.title}</div>
            <div className="text-xs">
              Type: {currentSlide.type} • Layout: {currentSlide.layout}
              {currentSlide.imageUrl && ' • Has image'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
