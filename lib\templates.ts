import { Template } from '@/types';

export const defaultTemplates: Template[] = [
  {
    _id: 'modern',
    name: 'Modern',
    description: 'Clean and professional design perfect for business presentations',
    thumbnail: '/templates/modern-preview.jpg',
    isPremium: false,
    styles: {
      backgroundColor: '#ffffff',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: {
        title: '2.5rem',
        heading: '1.5rem',
        body: '1rem',
      },
    },
    layouts: {
      title: 'centered',
      content: 'left-aligned',
      image: 'right-side',
    },
  },
  {
    _id: 'corporate',
    name: 'Corporate',
    description: 'Professional template ideal for corporate presentations and reports',
    thumbnail: '/templates/corporate-preview.jpg',
    isPremium: false,
    styles: {
      backgroundColor: '#f8fafc',
      primaryColor: '#1e293b',
      secondaryColor: '#475569',
      fontFamily: 'Arial, sans-serif',
      fontSize: {
        title: '2.25rem',
        heading: '1.375rem',
        body: '0.875rem',
      },
    },
    layouts: {
      title: 'left-aligned',
      content: 'structured',
      image: 'background',
    },
  },
  {
    _id: 'creative',
    name: 'Creative',
    description: 'Vibrant and engaging design for creative and marketing presentations',
    thumbnail: '/templates/creative-preview.jpg',
    isPremium: true,
    styles: {
      backgroundColor: '#fef3c7',
      primaryColor: '#f59e0b',
      secondaryColor: '#92400e',
      fontFamily: 'Poppins, sans-serif',
      fontSize: {
        title: '3rem',
        heading: '1.75rem',
        body: '1.125rem',
      },
    },
    layouts: {
      title: 'creative',
      content: 'dynamic',
      image: 'overlay',
    },
  },
  {
    _id: 'minimal',
    name: 'Minimal',
    description: 'Simple and elegant design focusing on content clarity',
    thumbnail: '/templates/minimal-preview.jpg',
    isPremium: false,
    styles: {
      backgroundColor: '#ffffff',
      primaryColor: '#000000',
      secondaryColor: '#6b7280',
      fontFamily: 'Georgia, serif',
      fontSize: {
        title: '2rem',
        heading: '1.25rem',
        body: '1rem',
      },
    },
    layouts: {
      title: 'centered',
      content: 'minimal',
      image: 'subtle',
    },
  },
  {
    _id: 'tech',
    name: 'Tech',
    description: 'Modern technology-focused design with gradients and bold typography',
    thumbnail: '/templates/tech-preview.jpg',
    isPremium: true,
    styles: {
      backgroundColor: '#0f172a',
      primaryColor: '#3b82f6',
      secondaryColor: '#94a3b8',
      fontFamily: 'JetBrains Mono, monospace',
      fontSize: {
        title: '2.75rem',
        heading: '1.5rem',
        body: '1rem',
      },
    },
    layouts: {
      title: 'tech-style',
      content: 'code-friendly',
      image: 'tech-overlay',
    },
  },
];

export class TemplateService {
  static getAllTemplates(): Template[] {
    return defaultTemplates;
  }

  static getFreeTemplates(): Template[] {
    return defaultTemplates.filter(template => !template.isPremium);
  }

  static getPremiumTemplates(): Template[] {
    return defaultTemplates.filter(template => template.isPremium);
  }

  static getTemplateById(id: string): Template | null {
    return defaultTemplates.find(template => template._id === id) || null;
  }

  static getTemplateByName(name: string): Template | null {
    return defaultTemplates.find(template => 
      template.name.toLowerCase() === name.toLowerCase()
    ) || null;
  }

  static applyTemplateToSlide(slideHtml: string, template: Template): string {
    // Apply template styles to slide HTML
    const styles = template.styles;
    
    return `
      <div class="slide" style="
        background-color: ${styles.backgroundColor};
        color: ${styles.primaryColor};
        font-family: ${styles.fontFamily};
        padding: 2rem;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
      ">
        <style>
          .slide h1 {
            font-size: ${styles.fontSize.title};
            color: ${styles.primaryColor};
            margin-bottom: 1rem;
          }
          .slide h2 {
            font-size: ${styles.fontSize.heading};
            color: ${styles.primaryColor};
            margin-bottom: 0.75rem;
          }
          .slide p, .slide li {
            font-size: ${styles.fontSize.body};
            color: ${styles.secondaryColor};
            line-height: 1.6;
          }
          .slide ul {
            list-style-type: disc;
            margin-left: 1.5rem;
          }
        </style>
        ${slideHtml}
      </div>
    `;
  }

  static generateSlideCSS(template: Template): string {
    const styles = template.styles;
    
    return `
      .presentation-slide {
        background-color: ${styles.backgroundColor};
        color: ${styles.primaryColor};
        font-family: ${styles.fontFamily};
        padding: 3rem;
        min-height: 600px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      .presentation-slide .slide-title {
        font-size: ${styles.fontSize.title};
        color: ${styles.primaryColor};
        margin-bottom: 1.5rem;
        font-weight: bold;
      }
      
      .presentation-slide .slide-heading {
        font-size: ${styles.fontSize.heading};
        color: ${styles.primaryColor};
        margin-bottom: 1rem;
        font-weight: 600;
      }
      
      .presentation-slide .slide-content {
        font-size: ${styles.fontSize.body};
        color: ${styles.secondaryColor};
        line-height: 1.7;
      }
      
      .presentation-slide .slide-content ul {
        list-style-type: disc;
        margin-left: 1.5rem;
        margin-top: 0.5rem;
      }
      
      .presentation-slide .slide-content li {
        margin-bottom: 0.5rem;
      }
      
      .presentation-slide .slide-image {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        margin: 1rem 0;
      }
      
      .presentation-slide.title-slide {
        text-align: center;
        justify-content: center;
      }
      
      .presentation-slide.content-slide {
        justify-content: flex-start;
      }
      
      .presentation-slide.image-slide {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
      }
    `;
  }

  static getTemplatePreviewHTML(template: Template): string {
    return `
      <div style="
        background-color: ${template.styles.backgroundColor};
        color: ${template.styles.primaryColor};
        font-family: ${template.styles.fontFamily};
        padding: 1rem;
        border-radius: 6px;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
      ">
        <h3 style="
          font-size: ${template.styles.fontSize.heading};
          color: ${template.styles.primaryColor};
          margin: 0 0 0.5rem 0;
          font-weight: bold;
        ">
          Sample Title
        </h3>
        <p style="
          font-size: ${template.styles.fontSize.body};
          color: ${template.styles.secondaryColor};
          margin: 0;
          line-height: 1.4;
        ">
          This is how your content will look with the ${template.name} template.
        </p>
      </div>
    `;
  }
}
