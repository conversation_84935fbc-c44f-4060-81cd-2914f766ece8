import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';
import { TemplateService } from '@/lib/templates';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'pdf';
    
    if (!id) {
      return NextResponse.json(
        { error: 'Presentation ID is required' },
        { status: 400 }
      );
    }

    if (!['pdf', 'png', 'jpg'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Supported formats: pdf, png, jpg' },
        { status: 400 }
      );
    }

    // Get presentation from database
    const presentation = await DatabaseService.getPresentationById(id);
    
    if (!presentation) {
      return NextResponse.json(
        { error: 'Presentation not found' },
        { status: 404 }
      );
    }

    // Get template
    const template = TemplateService.getTemplateById(presentation.template);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // For now, return presentation data for client-side export
    // In a production environment, you might want to do server-side rendering
    return NextResponse.json({
      success: true,
      presentation,
      template,
      format,
      message: 'Export data ready for client-side processing'
    });

  } catch (error) {
    console.error('Error preparing export:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to prepare export' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const body = await request.json();
    const { format = 'pdf', options = {} } = body;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Presentation ID is required' },
        { status: 400 }
      );
    }

    // Get presentation from database
    const presentation = await DatabaseService.getPresentationById(id);
    
    if (!presentation) {
      return NextResponse.json(
        { error: 'Presentation not found' },
        { status: 404 }
      );
    }

    // Get template
    const template = TemplateService.getTemplateById(presentation.template);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Return data for client-side export
    // In production, you could implement server-side PDF generation here
    return NextResponse.json({
      success: true,
      presentation,
      template,
      format,
      options,
      exportUrl: `/api/presentations/${id}/export?format=${format}`,
    });

  } catch (error) {
    console.error('Error in export API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process export request' 
      },
      { status: 500 }
    );
  }
}
