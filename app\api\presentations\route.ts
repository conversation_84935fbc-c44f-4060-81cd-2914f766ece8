import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = parseInt(searchParams.get('skip') || '0');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const presentations = await DatabaseService.getPresentationsByUserId(userId, limit, skip);
    
    return NextResponse.json({
      success: true,
      presentations,
      count: presentations.length,
    });
    
  } catch (error) {
    console.error('Error fetching presentations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch presentations' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.userId || !body.title || !body.topic) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, title, topic' },
        { status: 400 }
      );
    }

    const presentation = await DatabaseService.createPresentation(body);
    
    return NextResponse.json({
      success: true,
      presentation,
    });
    
  } catch (error) {
    console.error('Error creating presentation:', error);
    return NextResponse.json(
      { error: 'Failed to create presentation' },
      { status: 500 }
    );
  }
}
