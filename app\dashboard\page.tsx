'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import MainLayout from '@/components/layout/MainLayout';
import PresentationCard from '@/components/presentation/PresentationCard';
import { LoadingCard } from '@/components/ui/loading';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import { Presentation } from '@/types';

// Mock data for development
const mockPresentations: Presentation[] = [
  {
    _id: '1',
    userId: 'user1',
    title: 'Introduction to Machine Learning',
    topic: 'A comprehensive overview of machine learning concepts for business leaders',
    slides: [],
    template: 'modern',
    settings: {
      isPublic: false,
      allowComments: false,
    },
    metadata: {
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-16'),
      wordCount: 1250,
      slideCount: 12,
    },
  },
  {
    _id: '2',
    userId: 'user1',
    title: 'Q4 Sales Report',
    topic: 'Quarterly sales performance and market analysis',
    slides: [],
    template: 'corporate',
    settings: {
      isPublic: true,
      allowComments: true,
    },
    metadata: {
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-12'),
      wordCount: 890,
      slideCount: 8,
    },
  },
  {
    _id: '3',
    userId: 'user1',
    title: 'Product Launch Strategy',
    topic: 'Strategic plan for launching our new product line',
    slides: [],
    template: 'creative',
    settings: {
      isPublic: false,
      allowComments: false,
    },
    metadata: {
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-08'),
      wordCount: 1580,
      slideCount: 15,
    },
  },
];

const mockUser = {
  name: 'John Doe',
  email: '<EMAIL>',
  subscription: 'free' as const,
};

export default function DashboardPage() {
  const [presentations, setPresentations] = useState<Presentation[]>(mockPresentations);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(false);

  const filteredPresentations = presentations.filter(presentation =>
    presentation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    presentation.topic.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEdit = (id: string) => {
    console.log('Edit presentation:', id);
    // TODO: Navigate to editor
  };

  const handleDelete = (id: string) => {
    console.log('Delete presentation:', id);
    // TODO: Implement delete functionality
  };

  const handleShare = (id: string) => {
    console.log('Share presentation:', id);
    // TODO: Implement share functionality
  };

  const handleDuplicate = (id: string) => {
    console.log('Duplicate presentation:', id);
    // TODO: Implement duplicate functionality
  };

  const handleExport = (id: string) => {
    console.log('Export presentation:', id);
    // TODO: Implement export functionality
  };

  return (
    <MainLayout user={mockUser}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome back, {mockUser.name}
            </h1>
            <p className="text-gray-600">
              You have {presentations.length} presentations
            </p>
          </div>
          <Button size="lg" className="mt-4 sm:mt-0">
            <PlusIcon className="h-5 w-5 mr-2" />
            New Presentation
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Presentations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{presentations.length}</div>
              <p className="text-xs text-gray-500 mt-1">
                +2 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                This Month
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockUser.subscription === 'free' ? '1/3' : 'Unlimited'}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {mockUser.subscription === 'free' ? 'Free tier limit' : 'Pro subscription'}
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">
                Subscription
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Badge variant={mockUser.subscription === 'free' ? 'secondary' : 'default'}>
                  {mockUser.subscription}
                </Badge>
                {mockUser.subscription === 'free' && (
                  <Button variant="link" size="sm" className="p-0 h-auto">
                    Upgrade
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search presentations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <div className="flex border rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Squares2X2Icon className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <ListBulletIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Presentations Grid */}
        {isLoading ? (
          <LoadingCard title="Loading presentations..." />
        ) : filteredPresentations.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <PresentationChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <CardTitle className="mb-2">
                {searchQuery ? 'No presentations found' : 'No presentations yet'}
              </CardTitle>
              <CardDescription className="mb-4">
                {searchQuery 
                  ? 'Try adjusting your search terms'
                  : 'Create your first presentation to get started'
                }
              </CardDescription>
              {!searchQuery && (
                <Button>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Presentation
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {filteredPresentations.map((presentation) => (
              <PresentationCard
                key={presentation._id}
                presentation={presentation}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onShare={handleShare}
                onDuplicate={handleDuplicate}
                onExport={handleExport}
              />
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
