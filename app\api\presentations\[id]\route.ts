import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Presentation ID is required' },
        { status: 400 }
      );
    }

    const presentation = await DatabaseService.getPresentationById(id);
    
    if (!presentation) {
      return NextResponse.json(
        { error: 'Presentation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      presentation,
    });
    
  } catch (error) {
    console.error('Error fetching presentation:', error);
    return NextResponse.json(
      { error: 'Failed to fetch presentation' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const body = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Presentation ID is required' },
        { status: 400 }
      );
    }

    const updatedPresentation = await DatabaseService.updatePresentation(id, body);
    
    if (!updatedPresentation) {
      return NextResponse.json(
        { error: 'Presentation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      presentation: updatedPresentation,
    });
    
  } catch (error) {
    console.error('Error updating presentation:', error);
    return NextResponse.json(
      { error: 'Failed to update presentation' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Presentation ID is required' },
        { status: 400 }
      );
    }

    const deleted = await DatabaseService.deletePresentation(id);
    
    if (!deleted) {
      return NextResponse.json(
        { error: 'Presentation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Presentation deleted successfully',
    });
    
  } catch (error) {
    console.error('Error deleting presentation:', error);
    return NextResponse.json(
      { error: 'Failed to delete presentation' },
      { status: 500 }
    );
  }
}
