import { GoogleGenerativeAI } from '@google/generative-ai';
import { GenerationRequest, Presentation, Slide } from '@/types';
import { ImageService } from './image-service';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export class AIService {
  private static model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  static async generatePresentation(request: GenerationRequest): Promise<Presentation> {
    try {
      const startTime = Date.now();
      
      // Generate outline first
      const outline = await this.generateOutline(request);
      
      // Generate detailed content for each slide
      const slides = await this.generateSlideContent(outline, request);
      
      const endTime = Date.now();
      const generationTime = endTime - startTime;

      // Create presentation object
      const presentation: Presentation = {
        userId: 'temp-user', // Will be replaced with actual user ID
        title: this.extractTitle(request.topic),
        topic: request.topic,
        slides,
        template: 'modern', // Default template
        settings: {
          isPublic: false,
          allowComments: false,
        },
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          generationTime,
          wordCount: this.calculateWordCount(slides),
          slideCount: slides.length,
        },
        generationOptions: {
          audience: request.audience,
          tone: request.tone,
          length: request.slideCount,
        },
      };

      return presentation;
    } catch (error) {
      console.error('Error generating presentation:', error);
      throw new Error('Failed to generate presentation. Please try again.');
    }
  }

  private static async generateOutline(request: GenerationRequest): Promise<string[]> {
    const prompt = `
Create a detailed outline for a presentation about "${request.topic}".

Requirements:
- Target audience: ${request.audience}
- Tone: ${request.tone}
- Number of slides: ${request.slideCount}
- Include a title slide and conclusion slide

Please provide a structured outline with exactly ${request.slideCount} slide titles.
Each slide title should be clear, engaging, and appropriate for the ${request.audience} audience.
Use a ${request.tone} tone throughout.

Format your response as a numbered list of slide titles only, like:
1. Title Slide Title
2. Introduction Slide Title
3. Main Point 1 Title
...
${request.slideCount}. Conclusion Slide Title
`;

    const result = await this.model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    // Parse the outline into an array of slide titles
    const slideLines = text.split('\n').filter(line => line.trim().match(/^\d+\./));
    return slideLines.map(line => line.replace(/^\d+\.\s*/, '').trim());
  }

  private static async generateSlideContent(outline: string[], request: GenerationRequest): Promise<Slide[]> {
    const slides: Slide[] = [];

    for (let i = 0; i < outline.length; i++) {
      const slideTitle = outline[i];
      const slideType = this.determineSlideType(i, outline.length);
      
      const content = await this.generateSlideText(slideTitle, request, i, outline.length);
      
      const slide: Slide = {
        type: slideType,
        title: slideTitle,
        content,
        order: i,
        layout: 'default',
      };

      // Add image URL if images are requested
      if (request.includeImages && slideType !== 'title') {
        const imageQuery = await this.generateImageQuery(slideTitle, request.topic);
        try {
          const images = await ImageService.searchImages(imageQuery, 1);
          if (images.length > 0) {
            slide.imageUrl = images[0].url;
            slide.imageAlt = images[0].altText;
          }
        } catch (error) {
          console.warn('Failed to fetch image for slide:', slideTitle, error);
          // Continue without image
        }
      }

      slides.push(slide);
    }

    return slides;
  }

  private static async generateSlideText(
    slideTitle: string, 
    request: GenerationRequest, 
    slideIndex: number, 
    totalSlides: number
  ): Promise<string> {
    const isFirstSlide = slideIndex === 0;
    const isLastSlide = slideIndex === totalSlides - 1;
    
    let prompt = '';
    
    if (isFirstSlide) {
      prompt = `
Create content for a title slide with the title "${slideTitle}".
Topic: ${request.topic}
Audience: ${request.audience}
Tone: ${request.tone}

Provide a brief, engaging subtitle or description (2-3 lines) that introduces the presentation topic.
Keep it concise and compelling for the ${request.audience} audience.
`;
    } else if (isLastSlide) {
      prompt = `
Create content for a conclusion slide titled "${slideTitle}".
Topic: ${request.topic}
Audience: ${request.audience}
Tone: ${request.tone}

Provide:
- A brief summary of key points (3-4 bullet points)
- A call to action or next steps
- Keep it concise and impactful for the ${request.audience} audience
`;
    } else {
      prompt = `
Create detailed content for a slide titled "${slideTitle}".
Topic: ${request.topic}
Audience: ${request.audience}
Tone: ${request.tone}

Provide:
- 3-5 key bullet points or main ideas
- Brief explanations for each point
- Keep content appropriate for ${request.audience} audience
- Use a ${request.tone} tone
- Make it informative but not overwhelming
`;
    }

    const result = await this.model.generateContent(prompt);
    const response = result.response;
    return response.text().trim();
  }

  private static determineSlideType(index: number, totalSlides: number): Slide['type'] {
    if (index === 0) return 'title';
    if (index === totalSlides - 1) return 'conclusion';
    if (index === 1) return 'content'; // Introduction
    return 'content';
  }

  private static async generateImageQuery(slideTitle: string, topic: string): Promise<string> {
    // Generate a search query for Unsplash based on the slide title and topic
    const prompt = `
Generate a short, descriptive search query (2-4 words) for finding a relevant stock photo for a presentation slide.

Slide title: "${slideTitle}"
Presentation topic: "${topic}"

The query should be:
- Professional and appropriate for business presentations
- Specific enough to find relevant images
- Generic enough to have good search results
- Avoid overly specific or niche terms

Respond with only the search query, no additional text.
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      return response.text().trim().replace(/['"]/g, '');
    } catch (error) {
      // Fallback to a generic query based on the topic
      return topic.split(' ').slice(0, 2).join(' ');
    }
  }

  private static extractTitle(topic: string): string {
    // Extract a clean title from the topic
    const words = topic.split(' ');
    if (words.length <= 8) {
      return topic;
    }
    return words.slice(0, 8).join(' ') + '...';
  }

  private static calculateWordCount(slides: Slide[]): number {
    return slides.reduce((total, slide) => {
      const titleWords = slide.title.split(' ').length;
      const contentWords = slide.content.split(' ').length;
      return total + titleWords + contentWords;
    }, 0);
  }
}
