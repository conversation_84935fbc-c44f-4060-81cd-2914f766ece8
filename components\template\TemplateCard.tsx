'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Template } from '@/types';
import { TemplateService } from '@/lib/templates';
import { StarIcon } from '@heroicons/react/24/solid';

interface TemplateCardProps {
  template: Template;
  onSelect?: (template: Template) => void;
  isSelected?: boolean;
}

export default function TemplateCard({ template, onSelect, isSelected }: TemplateCardProps) {
  return (
    <Card 
      className={`group cursor-pointer transition-all duration-200 hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary' : ''
      }`}
      onClick={() => onSelect?.(template)}
    >
      <CardHeader className="pb-3">
        {/* Template Preview */}
        <div
          className="w-full h-32 rounded-md border overflow-hidden mb-3"
          style={{
            backgroundColor: template.styles.backgroundColor,
            borderColor: template.styles.primaryColor,
          }}
        >
          <div className="h-full flex items-center justify-center p-4">
            <div className="text-center">
              <div
                className="text-base font-bold mb-1"
                style={{
                  color: template.styles.primaryColor,
                  fontFamily: template.styles.fontFamily,
                  fontSize: '1.125rem'
                }}
              >
                Sample Title
              </div>
              <div
                className="text-xs"
                style={{
                  color: template.styles.secondaryColor,
                  fontFamily: template.styles.fontFamily,
                  fontSize: '0.75rem'
                }}
              >
                Content preview with {template.name} styling
              </div>
              <div
                className="w-8 h-1 mx-auto mt-2 rounded"
                style={{ backgroundColor: template.styles.primaryColor }}
              />
            </div>
          </div>
        </div>
        
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base font-semibold flex items-center">
              {template.name}
              {template.isPremium && (
                <StarIcon className="h-4 w-4 text-yellow-500 ml-2" />
              )}
            </CardTitle>
            <CardDescription className="mt-1 text-sm">
              {template.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {template.isPremium ? (
              <Badge variant="default" className="text-xs">
                Premium
              </Badge>
            ) : (
              <Badge variant="secondary" className="text-xs">
                Free
              </Badge>
            )}
          </div>
          
          <Button 
            size="sm" 
            variant={isSelected ? "default" : "outline"}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => {
              e.stopPropagation();
              onSelect?.(template);
            }}
          >
            {isSelected ? 'Selected' : 'Select'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
