'use client';

import React from 'react';
import { Slide, Template } from '@/types';
import { TemplateService } from '@/lib/templates';

interface SlideRendererProps {
  slide: Slide;
  template: Template;
  className?: string;
  isPreview?: boolean;
}

export default function SlideRenderer({ 
  slide, 
  template, 
  className = '',
  isPreview = false 
}: SlideRendererProps) {
  const renderSlideContent = () => {
    // Parse content into structured format
    const lines = slide.content.split('\n').filter(line => line.trim());
    
    return (
      <div className="slide-content">
        {lines.map((line, index) => {
          const trimmedLine = line.trim();
          
          // Check if it's a bullet point
          if (trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
            return (
              <li key={index} className="slide-bullet">
                {trimmedLine.substring(1).trim()}
              </li>
            );
          }
          
          // Check if it's a heading (starts with ##)
          if (trimmedLine.startsWith('##')) {
            return (
              <h3 key={index} className="slide-heading">
                {trimmedLine.substring(2).trim()}
              </h3>
            );
          }
          
          // Regular paragraph
          return (
            <p key={index} className="slide-paragraph">
              {trimmedLine}
            </p>
          );
        })}
      </div>
    );
  };

  const renderSlideLayout = () => {
    const hasImage = slide.imageUrl;
    
    if (slide.type === 'title') {
      return (
        <div className="title-slide">
          <h1 className="slide-title">{slide.title}</h1>
          <div className="slide-subtitle">
            {renderSlideContent()}
          </div>
        </div>
      );
    }
    
    if (hasImage && slide.layout === 'image-left') {
      return (
        <div className="image-left-layout">
          <div className="image-section">
            <img 
              src={slide.imageUrl} 
              alt={slide.imageAlt || slide.title}
              className="slide-image"
            />
          </div>
          <div className="content-section">
            <h2 className="slide-title">{slide.title}</h2>
            {renderSlideContent()}
          </div>
        </div>
      );
    }
    
    if (hasImage && slide.layout === 'image-right') {
      return (
        <div className="image-right-layout">
          <div className="content-section">
            <h2 className="slide-title">{slide.title}</h2>
            {renderSlideContent()}
          </div>
          <div className="image-section">
            <img 
              src={slide.imageUrl} 
              alt={slide.imageAlt || slide.title}
              className="slide-image"
            />
          </div>
        </div>
      );
    }
    
    // Default content layout
    return (
      <div className="content-slide">
        <h2 className="slide-title">{slide.title}</h2>
        {hasImage && (
          <img 
            src={slide.imageUrl} 
            alt={slide.imageAlt || slide.title}
            className="slide-image"
          />
        )}
        {renderSlideContent()}
      </div>
    );
  };

  const slideStyles = {
    backgroundColor: template.styles.backgroundColor,
    color: template.styles.primaryColor,
    fontFamily: template.styles.fontFamily,
  };

  return (
    <>
      <style jsx>{`
        .presentation-slide {
          background-color: ${template.styles.backgroundColor};
          color: ${template.styles.primaryColor};
          font-family: ${template.styles.fontFamily};
          padding: ${isPreview ? '1.5rem' : '3rem'};
          min-height: ${isPreview ? '300px' : '600px'};
          display: flex;
          flex-direction: column;
          justify-content: center;
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        
        .slide-title {
          font-size: ${isPreview ? '1.25rem' : template.styles.fontSize.title};
          color: ${template.styles.primaryColor};
          margin-bottom: 1.5rem;
          font-weight: bold;
          line-height: 1.2;
        }
        
        .slide-heading {
          font-size: ${isPreview ? '1rem' : template.styles.fontSize.heading};
          color: ${template.styles.primaryColor};
          margin: 1rem 0 0.5rem 0;
          font-weight: 600;
        }
        
        .slide-content {
          font-size: ${isPreview ? '0.875rem' : template.styles.fontSize.body};
          color: ${template.styles.secondaryColor};
          line-height: 1.7;
        }
        
        .slide-paragraph {
          margin-bottom: 1rem;
        }
        
        .slide-bullet {
          margin-bottom: 0.5rem;
          margin-left: 1.5rem;
          list-style-type: disc;
        }
        
        .slide-image {
          max-width: 100%;
          height: auto;
          border-radius: 6px;
          margin: 1rem 0;
        }
        
        .title-slide {
          text-align: center;
          justify-content: center;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        
        .content-slide {
          justify-content: flex-start;
        }
        
        .image-left-layout,
        .image-right-layout {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 2rem;
          align-items: center;
          height: 100%;
        }
        
        .image-section {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .content-section {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
        
        .slide-subtitle {
          font-size: ${isPreview ? '0.75rem' : '1.125rem'};
          color: ${template.styles.secondaryColor};
          margin-top: 1rem;
        }
      `}</style>
      
      <div 
        className={`presentation-slide ${className}`}
        style={slideStyles}
      >
        {renderSlideLayout()}
      </div>
    </>
  );
}
