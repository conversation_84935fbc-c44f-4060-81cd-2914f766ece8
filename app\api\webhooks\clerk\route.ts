import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { DatabaseService } from '@/lib/database';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

export async function POST(req: NextRequest) {
  if (!webhookSecret) {
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');
  }

  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as any;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error occured', {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;

  if (eventType === 'user.created') {
    const { id, email_addresses, first_name, last_name } = evt.data;
    
    try {
      // Create user in our database
      await DatabaseService.createUser({
        clerkId: id,
        email: email_addresses[0]?.email_address || '',
        name: `${first_name || ''} ${last_name || ''}`.trim() || 'User',
        subscription: 'free',
        usage: {
          presentations: 0,
          imagesGenerated: 0,
          exportsThisMonth: 0,
        },
        preferences: {
          defaultTemplate: 'modern',
          autoSave: true,
        },
      });

      console.log('User created successfully:', id);
    } catch (error) {
      console.error('Error creating user:', error);
      return new Response('Error creating user', { status: 500 });
    }
  }

  if (eventType === 'user.updated') {
    const { id, email_addresses, first_name, last_name } = evt.data;
    
    try {
      // Update user in our database
      const user = await DatabaseService.getUserByClerkId(id);
      if (user) {
        await DatabaseService.updateUser(user._id!, {
          email: email_addresses[0]?.email_address || user.email,
          name: `${first_name || ''} ${last_name || ''}`.trim() || user.name,
        });
      }

      console.log('User updated successfully:', id);
    } catch (error) {
      console.error('Error updating user:', error);
      return new Response('Error updating user', { status: 500 });
    }
  }

  if (eventType === 'user.deleted') {
    const { id } = evt.data;
    
    try {
      // Delete user from our database
      const user = await DatabaseService.getUserByClerkId(id);
      if (user) {
        // In a production app, you might want to soft delete or archive the user
        // For now, we'll just log it
        console.log('User deleted:', id);
      }
    } catch (error) {
      console.error('Error handling user deletion:', error);
      return new Response('Error handling user deletion', { status: 500 });
    }
  }

  return new Response('', { status: 200 });
}
