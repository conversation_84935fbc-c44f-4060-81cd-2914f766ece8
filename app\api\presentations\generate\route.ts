import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { AIService } from '@/lib/ai-service';
import { DatabaseService } from '@/lib/database';
import { GenerationRequest, GenerationResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: GenerationRequest = await request.json();
    
    // Validate request body
    if (!body.topic || !body.topic.trim()) {
      return NextResponse.json(
        { success: false, error: 'Topic is required' },
        { status: 400 }
      );
    }

    if (!body.audience || !body.tone || !body.slideCount) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (body.slideCount < 3 || body.slideCount > 50) {
      return NextResponse.json(
        { success: false, error: 'Slide count must be between 3 and 50' },
        { status: 400 }
      );
    }

    // Get user from database
    const user = await DatabaseService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Generate presentation using AI
    const presentation = await AIService.generatePresentation(body);

    // Set the actual user ID
    presentation.userId = user._id!;

    // Save to database
    const savedPresentation = await DatabaseService.createPresentation(presentation);
    
    const response: GenerationResponse = {
      success: true,
      presentation: savedPresentation,
      generationTime: presentation.metadata.generationTime,
    };

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error in presentation generation:', error);
    
    const response: GenerationResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
    
    return NextResponse.json(response, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
