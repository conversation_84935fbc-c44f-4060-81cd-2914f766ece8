import { NextRequest, NextResponse } from 'next/server';
import { ImageService } from '@/lib/image-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const count = parseInt(searchParams.get('count') || '10');
    const collections = searchParams.get('collections')?.split(',').filter(Boolean);
    const topics = searchParams.get('topics')?.split(',').filter(Boolean);

    if (count < 1 || count > 30) {
      return NextResponse.json(
        { success: false, error: 'Count must be between 1 and 30' },
        { status: 400 }
      );
    }

    const images = await ImageService.getRandomImages(count, collections, topics);

    return NextResponse.json({
      success: true,
      images,
      count: images.length,
    });

  } catch (error) {
    console.error('Error in random images API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch random images' 
      },
      { status: 500 }
    );
  }
}
