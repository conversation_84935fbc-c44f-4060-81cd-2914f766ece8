import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Presentation, Template } from '@/types';
import { TemplateService } from './templates';

export interface ExportOptions {
  format: 'pdf' | 'png' | 'jpg';
  quality?: number;
  includeNotes?: boolean;
  slideSize?: 'standard' | 'widescreen';
}

export class ExportService {
  private static readonly SLIDE_DIMENSIONS = {
    standard: { width: 800, height: 600 },
    widescreen: { width: 1280, height: 720 },
  };

  static async exportToPDF(
    presentation: Presentation,
    template: Template,
    options: ExportOptions = { format: 'pdf' }
  ): Promise<Blob> {
    const { slideSize = 'widescreen' } = options;
    const dimensions = this.SLIDE_DIMENSIONS[slideSize];
    
    // Create PDF with appropriate dimensions
    const pdf = new jsPDF({
      orientation: slideSize === 'widescreen' ? 'landscape' : 'landscape',
      unit: 'px',
      format: [dimensions.width, dimensions.height],
    });

    // Generate slides
    for (let i = 0; i < presentation.slides.length; i++) {
      const slide = presentation.slides[i];
      
      if (i > 0) {
        pdf.addPage();
      }

      // Create slide HTML
      const slideHTML = this.generateSlideHTML(slide, template, dimensions);
      
      // Create temporary container
      const container = document.createElement('div');
      container.innerHTML = slideHTML;
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.style.width = `${dimensions.width}px`;
      container.style.height = `${dimensions.height}px`;
      document.body.appendChild(container);

      try {
        // Convert to canvas
        const canvas = await html2canvas(container, {
          width: dimensions.width,
          height: dimensions.height,
          scale: 2, // Higher quality
          useCORS: true,
          allowTaint: true,
          backgroundColor: template.styles.backgroundColor,
        });

        // Add to PDF
        const imgData = canvas.toDataURL('image/jpeg', 0.95);
        pdf.addImage(imgData, 'JPEG', 0, 0, dimensions.width, dimensions.height);
        
      } finally {
        // Clean up
        document.body.removeChild(container);
      }
    }

    // Return as blob
    return new Promise((resolve) => {
      const pdfBlob = pdf.output('blob');
      resolve(pdfBlob);
    });
  }

  static async exportSlideAsImage(
    slide: any,
    template: Template,
    options: ExportOptions = { format: 'png' }
  ): Promise<Blob> {
    const { slideSize = 'widescreen', quality = 0.95 } = options;
    const dimensions = this.SLIDE_DIMENSIONS[slideSize];
    
    // Create slide HTML
    const slideHTML = this.generateSlideHTML(slide, template, dimensions);
    
    // Create temporary container
    const container = document.createElement('div');
    container.innerHTML = slideHTML;
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.width = `${dimensions.width}px`;
    container.style.height = `${dimensions.height}px`;
    document.body.appendChild(container);

    try {
      // Convert to canvas
      const canvas = await html2canvas(container, {
        width: dimensions.width,
        height: dimensions.height,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: template.styles.backgroundColor,
      });

      // Convert to blob
      return new Promise((resolve) => {
        canvas.toBlob(
          (blob) => resolve(blob!),
          options.format === 'jpg' ? 'image/jpeg' : 'image/png',
          quality
        );
      });
      
    } finally {
      // Clean up
      document.body.removeChild(container);
    }
  }

  private static generateSlideHTML(slide: any, template: Template, dimensions: any): string {
    const styles = template.styles;
    
    // Parse slide content
    const lines = slide.content.split('\n').filter((line: string) => line.trim());
    let contentHTML = '';
    
    lines.forEach((line: string) => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
        contentHTML += `<li style="margin-bottom: 0.5rem; margin-left: 1.5rem; list-style-type: disc;">${trimmedLine.substring(1).trim()}</li>`;
      } else if (trimmedLine.startsWith('##')) {
        contentHTML += `<h3 style="font-size: ${styles.fontSize.heading}; color: ${styles.primaryColor}; margin: 1rem 0 0.5rem 0; font-weight: 600;">${trimmedLine.substring(2).trim()}</h3>`;
      } else {
        contentHTML += `<p style="margin-bottom: 1rem; line-height: 1.7;">${trimmedLine}</p>`;
      }
    });

    // Determine layout
    let layoutHTML = '';
    const hasImage = slide.imageUrl;
    
    if (slide.type === 'title') {
      layoutHTML = `
        <div style="text-align: center; display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
          <h1 style="font-size: ${styles.fontSize.title}; color: ${styles.primaryColor}; margin-bottom: 1.5rem; font-weight: bold; line-height: 1.2;">${slide.title}</h1>
          <div style="font-size: 1.125rem; color: ${styles.secondaryColor}; margin-top: 1rem;">
            ${contentHTML}
          </div>
        </div>
      `;
    } else if (hasImage && slide.layout === 'image-left') {
      layoutHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center; height: 100%;">
          <div style="display: flex; align-items: center; justify-content: center;">
            <img src="${slide.imageUrl}" alt="${slide.imageAlt || slide.title}" style="max-width: 100%; height: auto; border-radius: 6px;" />
          </div>
          <div>
            <h2 style="font-size: ${styles.fontSize.title}; color: ${styles.primaryColor}; margin-bottom: 1.5rem; font-weight: bold;">${slide.title}</h2>
            <div style="font-size: ${styles.fontSize.body}; color: ${styles.secondaryColor};">
              ${contentHTML}
            </div>
          </div>
        </div>
      `;
    } else if (hasImage && slide.layout === 'image-right') {
      layoutHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center; height: 100%;">
          <div>
            <h2 style="font-size: ${styles.fontSize.title}; color: ${styles.primaryColor}; margin-bottom: 1.5rem; font-weight: bold;">${slide.title}</h2>
            <div style="font-size: ${styles.fontSize.body}; color: ${styles.secondaryColor};">
              ${contentHTML}
            </div>
          </div>
          <div style="display: flex; align-items: center; justify-content: center;">
            <img src="${slide.imageUrl}" alt="${slide.imageAlt || slide.title}" style="max-width: 100%; height: auto; border-radius: 6px;" />
          </div>
        </div>
      `;
    } else {
      // Default content layout
      layoutHTML = `
        <div style="display: flex; flex-direction: column; justify-content: flex-start; height: 100%;">
          <h2 style="font-size: ${styles.fontSize.title}; color: ${styles.primaryColor}; margin-bottom: 1.5rem; font-weight: bold;">${slide.title}</h2>
          ${hasImage ? `<img src="${slide.imageUrl}" alt="${slide.imageAlt || slide.title}" style="max-width: 100%; height: auto; border-radius: 6px; margin: 1rem 0;" />` : ''}
          <div style="font-size: ${styles.fontSize.body}; color: ${styles.secondaryColor};">
            ${contentHTML}
          </div>
        </div>
      `;
    }

    return `
      <div style="
        background-color: ${styles.backgroundColor};
        color: ${styles.primaryColor};
        font-family: ${styles.fontFamily};
        padding: 3rem;
        width: ${dimensions.width}px;
        height: ${dimensions.height}px;
        box-sizing: border-box;
        overflow: hidden;
      ">
        ${layoutHTML}
      </div>
    `;
  }

  static generateFileName(presentation: Presentation, format: string): string {
    const cleanTitle = presentation.title
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .toLowerCase();
    
    const timestamp = new Date().toISOString().split('T')[0];
    return `${cleanTitle}-${timestamp}.${format}`;
  }

  static downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
