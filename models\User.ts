import mongoose, { Schema, Document } from 'mongoose';
import { User } from '@/types';

export interface UserDocument extends User, Document {}

const UserSchema = new Schema<UserDocument>({
  clerkId: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
  },
  subscription: {
    type: String,
    enum: ['free', 'pro', 'team'],
    default: 'free',
  },
  usage: {
    presentations: {
      type: Number,
      default: 0,
    },
    imagesGenerated: {
      type: Number,
      default: 0,
    },
    exportsThisMonth: {
      type: Number,
      default: 0,
    },
  },
  preferences: {
    defaultTemplate: {
      type: String,
      default: 'modern',
    },
    autoSave: {
      type: Boolean,
      default: true,
    },
  },
}, {
  timestamps: true,
});

// Indexes for better performance
UserSchema.index({ email: 1 });
UserSchema.index({ subscription: 1 });

export default mongoose.models.User || mongoose.model<UserDocument>('User', UserSchema);
