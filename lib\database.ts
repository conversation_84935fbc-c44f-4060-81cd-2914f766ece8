import connectDB from './mongodb';
import User from '@/models/User';
import Presentation from '@/models/Presentation';
import Template from '@/models/Template';
import { User as UserType, Presentation as PresentationType, Template as TemplateType } from '@/types';

export class DatabaseService {
  static async init() {
    await connectDB();
  }

  // User operations
  static async createUser(userData: Omit<UserType, '_id' | 'createdAt' | 'updatedAt'>): Promise<UserType> {
    await this.init();
    const user = new User(userData);
    await user.save();
    return user.toObject();
  }

  static async getUserByEmail(email: string): Promise<UserType | null> {
    await this.init();
    const user = await User.findOne({ email });
    return user ? user.toObject() : null;
  }

  static async getUserById(id: string): Promise<UserType | null> {
    await this.init();
    const user = await User.findById(id);
    return user ? user.toObject() : null;
  }

  static async updateUser(id: string, updates: Partial<UserType>): Promise<UserType | null> {
    await this.init();
    const user = await User.findByIdAndUpdate(id, updates, { new: true });
    return user ? user.toObject() : null;
  }

  // Presentation operations
  static async createPresentation(presentationData: Omit<PresentationType, '_id'>): Promise<PresentationType> {
    await this.init();
    const presentation = new Presentation(presentationData);
    await presentation.save();
    return presentation.toObject();
  }

  static async getPresentationById(id: string): Promise<PresentationType | null> {
    await this.init();
    const presentation = await Presentation.findById(id);
    return presentation ? presentation.toObject() : null;
  }

  static async getPresentationsByUserId(userId: string, limit = 20, skip = 0): Promise<PresentationType[]> {
    await this.init();
    const presentations = await Presentation.find({ userId })
      .sort({ 'metadata.createdAt': -1 })
      .limit(limit)
      .skip(skip);
    return presentations.map(p => p.toObject());
  }

  static async updatePresentation(id: string, updates: Partial<PresentationType>): Promise<PresentationType | null> {
    await this.init();
    const presentation = await Presentation.findByIdAndUpdate(id, updates, { new: true });
    return presentation ? presentation.toObject() : null;
  }

  static async deletePresentation(id: string): Promise<boolean> {
    await this.init();
    const result = await Presentation.findByIdAndDelete(id);
    return !!result;
  }

  // Template operations
  static async getAllTemplates(): Promise<TemplateType[]> {
    await this.init();
    const templates = await Template.find().sort({ name: 1 });
    return templates.map(t => t.toObject());
  }

  static async getTemplateById(id: string): Promise<TemplateType | null> {
    await this.init();
    const template = await Template.findById(id);
    return template ? template.toObject() : null;
  }

  static async getFreeTemplates(): Promise<TemplateType[]> {
    await this.init();
    const templates = await Template.find({ isPremium: false }).sort({ name: 1 });
    return templates.map(t => t.toObject());
  }

  // Search operations
  static async searchPresentations(userId: string, query: string): Promise<PresentationType[]> {
    await this.init();
    const presentations = await Presentation.find({
      userId,
      $text: { $search: query }
    }).sort({ score: { $meta: 'textScore' } });
    return presentations.map(p => p.toObject());
  }
}
