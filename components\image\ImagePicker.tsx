'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  MagnifyingGlassIcon,
  PhotoIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { ApiClient } from '@/lib/api-client';
import { ImageSearchResult } from '@/lib/image-service';

interface ImagePickerProps {
  onImageSelect: (image: ImageSearchResult) => void;
  selectedImageId?: string;
  searchQuery?: string;
  trigger?: React.ReactNode;
}

export default function ImagePicker({ 
  onImageSelect, 
  selectedImageId, 
  searchQuery = '',
  trigger 
}: ImagePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [images, setImages] = useState<ImageSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState(searchQuery);
  const [selectedImage, setSelectedImage] = useState<ImageSearchResult | null>(null);

  useEffect(() => {
    if (isOpen && searchQuery) {
      handleSearch(searchQuery);
    } else if (isOpen) {
      loadRandomImages();
    }
  }, [isOpen, searchQuery]);

  const handleSearch = async (searchTerm: string) => {
    if (!searchTerm.trim()) return;
    
    setLoading(true);
    try {
      const response = await ApiClient.searchImages(searchTerm.trim(), 20);
      if (response.success) {
        setImages(response.images);
      }
    } catch (error) {
      console.error('Error searching images:', error);
      // Show fallback images or error state
      setImages([]);
    } finally {
      setLoading(false);
    }
  };

  const loadRandomImages = async () => {
    setLoading(true);
    try {
      // Use a generic business/professional topic for random images
      const response = await ApiClient.searchImages('business professional', 20);
      if (response.success) {
        setImages(response.images);
      }
    } catch (error) {
      console.error('Error loading random images:', error);
      setImages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (image: ImageSearchResult) => {
    setSelectedImage(image);
  };

  const handleConfirmSelection = () => {
    if (selectedImage) {
      onImageSelect(selectedImage);
      setIsOpen(false);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(query);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <PhotoIcon className="h-4 w-4 mr-2" />
            Choose Image
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Choose an Image</DialogTitle>
          <DialogDescription>
            Search for professional stock photos to enhance your presentation
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search Bar */}
          <form onSubmit={handleSearchSubmit} className="flex gap-2">
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search for images..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" disabled={loading}>
              Search
            </Button>
          </form>

          {/* Image Grid */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner size="lg" />
                <span className="ml-3 text-sm text-gray-600">Searching images...</span>
              </div>
            ) : images.length === 0 ? (
              <div className="text-center py-12">
                <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No images found. Try a different search term.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {images.map((image) => (
                  <Card 
                    key={image.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedImage?.id === image.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => handleImageClick(image)}
                  >
                    <CardContent className="p-0">
                      <div className="relative">
                        <img
                          src={image.thumbnailUrl}
                          alt={image.altText}
                          className="w-full h-32 object-cover rounded-t-lg"
                          loading="lazy"
                        />
                        {selectedImage?.id === image.id && (
                          <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                            <CheckIcon className="h-3 w-3" />
                          </div>
                        )}
                      </div>
                      <div className="p-2">
                        <p className="text-xs text-gray-600 truncate">
                          by {image.photographer}
                        </p>
                        {image.description && (
                          <p className="text-xs text-gray-500 truncate mt-1">
                            {image.description}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Selected Image Preview */}
          {selectedImage && (
            <div className="border-t pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <img
                    src={selectedImage.thumbnailUrl}
                    alt={selectedImage.altText}
                    className="w-16 h-12 object-cover rounded"
                  />
                  <div>
                    <p className="text-sm font-medium">Selected Image</p>
                    <p className="text-xs text-gray-600">
                      by {selectedImage.photographer}
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" onClick={() => setSelectedImage(null)}>
                    Cancel
                  </Button>
                  <Button onClick={handleConfirmSelection}>
                    Use This Image
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Attribution */}
          <div className="text-xs text-gray-500 border-t pt-2">
            <p>
              Images provided by{' '}
              <a 
                href="https://unsplash.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="underline hover:text-gray-700"
              >
                Unsplash
              </a>
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
