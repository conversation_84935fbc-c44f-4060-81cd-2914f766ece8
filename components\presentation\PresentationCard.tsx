'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  EllipsisVerticalIcon, 
  PencilIcon, 
  TrashIcon, 
  ShareIcon,
  DocumentDuplicateIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { Presentation } from '@/types';

interface PresentationCardProps {
  presentation: Presentation;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onShare?: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onExport?: (id: string) => void;
}

export default function PresentationCard({
  presentation,
  onEdit,
  onDelete,
  onShare,
  onDuplicate,
  onExport,
}: PresentationCardProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date));
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(date);
  };

  return (
    <Card className="group hover:shadow-md transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold truncate">
              {presentation.title}
            </CardTitle>
            <CardDescription className="mt-1 line-clamp-2">
              {presentation.topic}
            </CardDescription>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <EllipsisVerticalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(presentation._id!)}>
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
              )}
              {onDuplicate && (
                <DropdownMenuItem onClick={() => onDuplicate(presentation._id!)}>
                  <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
              )}
              {onShare && (
                <DropdownMenuItem onClick={() => onShare(presentation._id!)}>
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share
                </DropdownMenuItem>
              )}
              {onExport && (
                <DropdownMenuItem onClick={() => onExport(presentation._id!)}>
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Export
                </DropdownMenuItem>
              )}
              {onDelete && (
                <>
                  <DropdownMenuItem 
                    onClick={() => onDelete(presentation._id!)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
          <div className="flex items-center space-x-4">
            <span>{presentation.metadata.slideCount} slides</span>
            <span>{presentation.metadata.wordCount} words</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {presentation.template}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">
            Updated {getTimeAgo(presentation.metadata.updatedAt)}
          </span>
          
          <div className="flex items-center space-x-2">
            {presentation.settings.isPublic && (
              <Badge variant="outline" className="text-xs">
                Public
              </Badge>
            )}
            <Button 
              size="sm" 
              onClick={() => onEdit?.(presentation._id!)}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              Open
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
