import mongoose, { Schema, Document } from 'mongoose';
import { Template } from '@/types';

export interface TemplateDocument extends Template, Document {}

const TemplateSchema = new Schema<TemplateDocument>({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  thumbnail: {
    type: String,
    required: true,
    trim: true,
  },
  isPremium: {
    type: Boolean,
    default: false,
  },
  styles: {
    backgroundColor: {
      type: String,
      required: true,
    },
    primaryColor: {
      type: String,
      required: true,
    },
    secondaryColor: {
      type: String,
      required: true,
    },
    fontFamily: {
      type: String,
      required: true,
    },
    fontSize: {
      title: {
        type: String,
        required: true,
      },
      heading: {
        type: String,
        required: true,
      },
      body: {
        type: String,
        required: true,
      },
    },
  },
  layouts: {
    title: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      required: true,
    },
  },
}, {
  timestamps: true,
});

// Indexes
TemplateSchema.index({ name: 1 });
TemplateSchema.index({ isPremium: 1 });

export default mongoose.models.Template || mongoose.model<TemplateDocument>('Template', TemplateSchema);
