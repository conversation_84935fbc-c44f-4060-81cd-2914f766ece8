import axios from 'axios';

const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY;
const UNSPLASH_API_URL = 'https://api.unsplash.com';

export interface UnsplashImage {
  id: string;
  urls: {
    raw: string;
    full: string;
    regular: string;
    small: string;
    thumb: string;
  };
  alt_description: string | null;
  description: string | null;
  user: {
    name: string;
    username: string;
  };
  links: {
    download_location: string;
  };
  width: number;
  height: number;
}

export interface ImageSearchResult {
  id: string;
  url: string;
  thumbnailUrl: string;
  altText: string;
  description: string;
  photographer: string;
  photographerUrl: string;
  downloadUrl: string;
  width: number;
  height: number;
}

export class ImageService {
  private static validateApiKey(): void {
    if (!UNSPLASH_ACCESS_KEY) {
      throw new Error('Unsplash API key is not configured');
    }
  }

  static async searchImages(
    query: string, 
    count: number = 10,
    orientation: 'landscape' | 'portrait' | 'squarish' = 'landscape'
  ): Promise<ImageSearchResult[]> {
    this.validateApiKey();

    try {
      const response = await axios.get(`${UNSPLASH_API_URL}/search/photos`, {
        params: {
          query,
          per_page: Math.min(count, 30), // Unsplash max is 30
          orientation,
          content_filter: 'high', // Filter out potentially inappropriate content
        },
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
        },
      });

      const images: UnsplashImage[] = response.data.results;
      
      return images.map(this.transformUnsplashImage);
    } catch (error) {
      console.error('Error searching images:', error);
      
      // Return fallback images or empty array
      return this.getFallbackImages(query, count);
    }
  }

  static async getImageById(id: string): Promise<ImageSearchResult | null> {
    this.validateApiKey();

    try {
      const response = await axios.get(`${UNSPLASH_API_URL}/photos/${id}`, {
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
        },
      });

      return this.transformUnsplashImage(response.data);
    } catch (error) {
      console.error('Error fetching image by ID:', error);
      return null;
    }
  }

  static async downloadImage(downloadUrl: string): Promise<void> {
    this.validateApiKey();

    try {
      // Trigger download tracking (required by Unsplash API)
      await axios.get(downloadUrl, {
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
        },
      });
    } catch (error) {
      console.error('Error tracking image download:', error);
      // Don't throw error as this is just for tracking
    }
  }

  static async getRandomImages(
    count: number = 10,
    collections?: string[],
    topics?: string[]
  ): Promise<ImageSearchResult[]> {
    this.validateApiKey();

    try {
      const params: any = {
        count: Math.min(count, 30),
        content_filter: 'high',
      };

      if (collections && collections.length > 0) {
        params.collections = collections.join(',');
      }

      if (topics && topics.length > 0) {
        params.topics = topics.join(',');
      }

      const response = await axios.get(`${UNSPLASH_API_URL}/photos/random`, {
        params,
        headers: {
          'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`,
        },
      });

      const images = Array.isArray(response.data) ? response.data : [response.data];
      
      return images.map(this.transformUnsplashImage);
    } catch (error) {
      console.error('Error fetching random images:', error);
      return this.getFallbackImages('business', count);
    }
  }

  private static transformUnsplashImage(image: UnsplashImage): ImageSearchResult {
    return {
      id: image.id,
      url: image.urls.regular,
      thumbnailUrl: image.urls.small,
      altText: image.alt_description || image.description || 'Stock photo',
      description: image.description || image.alt_description || '',
      photographer: image.user.name,
      photographerUrl: `https://unsplash.com/@${image.user.username}`,
      downloadUrl: image.links.download_location,
      width: image.width,
      height: image.height,
    };
  }

  private static getFallbackImages(query: string, count: number): ImageSearchResult[] {
    // Return placeholder images when Unsplash is not available
    const fallbackImages: ImageSearchResult[] = [];
    
    for (let i = 0; i < count; i++) {
      fallbackImages.push({
        id: `fallback-${i}`,
        url: `https://via.placeholder.com/800x600/4F46E5/FFFFFF?text=${encodeURIComponent(query)}`,
        thumbnailUrl: `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${encodeURIComponent(query)}`,
        altText: `Placeholder image for ${query}`,
        description: `Placeholder image related to ${query}`,
        photographer: 'Placeholder',
        photographerUrl: '#',
        downloadUrl: '#',
        width: 800,
        height: 600,
      });
    }
    
    return fallbackImages;
  }

  static getOptimizedImageUrl(
    originalUrl: string, 
    width?: number, 
    height?: number, 
    quality: number = 80
  ): string {
    // If it's an Unsplash URL, we can use their image transformation API
    if (originalUrl.includes('images.unsplash.com')) {
      const url = new URL(originalUrl);
      
      if (width) url.searchParams.set('w', width.toString());
      if (height) url.searchParams.set('h', height.toString());
      url.searchParams.set('q', quality.toString());
      url.searchParams.set('fm', 'webp'); // Use WebP format for better compression
      
      return url.toString();
    }
    
    return originalUrl;
  }

  static generateImageSearchQueries(topic: string, slideTitle: string): string[] {
    // Generate multiple search queries to increase chances of finding relevant images
    const queries: string[] = [];
    
    // Primary query based on slide title
    queries.push(slideTitle.toLowerCase().replace(/[^\w\s]/g, '').trim());
    
    // Secondary query based on topic
    const topicWords = topic.toLowerCase().split(' ').slice(0, 3);
    queries.push(topicWords.join(' '));
    
    // Generic business/professional queries as fallbacks
    queries.push('business professional');
    queries.push('office meeting');
    queries.push('technology modern');
    
    return queries.filter(q => q.length > 0);
  }
}
