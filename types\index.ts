export interface User {
  _id?: string;
  email: string;
  name: string;
  subscription: 'free' | 'pro' | 'team';
  usage: {
    presentations: number;
    imagesGenerated: number;
    exportsThisMonth: number;
  };
  preferences: {
    defaultTemplate: string;
    autoSave: boolean;
  };
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Slide {
  _id?: string;
  type: 'title' | 'content' | 'image' | 'chart' | 'conclusion';
  title: string;
  content: string;
  imageUrl?: string;
  imageAlt?: string;
  order: number;
  layout: 'default' | 'image-left' | 'image-right' | 'image-full' | 'text-only';
}

export interface Presentation {
  _id?: string;
  userId: string;
  title: string;
  topic: string;
  description?: string;
  slides: Slide[];
  template: string;
  settings: {
    isPublic: boolean;
    allowComments: boolean;
    shareLink?: string;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    generationTime?: number;
    wordCount: number;
    slideCount: number;
  };
  generationOptions?: {
    audience: 'business' | 'academic' | 'general' | 'technical';
    tone: 'professional' | 'casual' | 'academic' | 'creative';
    length: number; // number of slides
  };
}

export interface Template {
  _id?: string;
  name: string;
  description: string;
  thumbnail: string;
  isPremium: boolean;
  styles: {
    backgroundColor: string;
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: {
      title: string;
      heading: string;
      body: string;
    };
  };
  layouts: {
    title: string;
    content: string;
    image: string;
  };
}

export interface GenerationRequest {
  topic: string;
  audience: 'business' | 'academic' | 'general' | 'technical';
  tone: 'professional' | 'casual' | 'academic' | 'creative';
  slideCount: number;
  includeImages: boolean;
}

export interface GenerationResponse {
  success: boolean;
  presentation?: Presentation;
  error?: string;
  generationTime?: number;
}
