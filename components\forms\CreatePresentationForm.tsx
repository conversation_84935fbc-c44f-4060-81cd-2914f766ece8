'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { GenerationRequest } from '@/types';

interface CreatePresentationFormProps {
  onSubmit: (data: GenerationRequest) => void;
  isLoading?: boolean;
}

export default function CreatePresentationForm({ onSubmit, isLoading }: CreatePresentationFormProps) {
  const [formData, setFormData] = useState<GenerationRequest>({
    topic: '',
    audience: 'general',
    tone: 'professional',
    slideCount: 10,
    includeImages: true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.topic.trim()) {
      onSubmit(formData);
    }
  };

  const updateFormData = (field: keyof GenerationRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Presentation</CardTitle>
        <CardDescription>
          Tell us about your presentation and we'll generate it for you using AI.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Topic Input */}
          <div className="space-y-2">
            <Label htmlFor="topic">Presentation Topic *</Label>
            <Textarea
              id="topic"
              placeholder="e.g., Introduction to Machine Learning for Business Leaders"
              value={formData.topic}
              onChange={(e) => updateFormData('topic', e.target.value)}
              className="min-h-[100px]"
              required
            />
            <p className="text-xs text-muted-foreground">
              Describe what you want your presentation to be about. Be as specific as possible.
            </p>
          </div>

          {/* Audience Selection */}
          <div className="space-y-2">
            <Label htmlFor="audience">Target Audience</Label>
            <Select 
              value={formData.audience} 
              onValueChange={(value: any) => updateFormData('audience', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select your audience" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Audience</SelectItem>
                <SelectItem value="business">Business Professionals</SelectItem>
                <SelectItem value="academic">Academic/Educational</SelectItem>
                <SelectItem value="technical">Technical/Engineering</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tone Selection */}
          <div className="space-y-2">
            <Label htmlFor="tone">Presentation Tone</Label>
            <Select 
              value={formData.tone} 
              onValueChange={(value: any) => updateFormData('tone', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select presentation tone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual & Friendly</SelectItem>
                <SelectItem value="academic">Academic & Formal</SelectItem>
                <SelectItem value="creative">Creative & Engaging</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Slide Count */}
          <div className="space-y-2">
            <Label htmlFor="slideCount">Number of Slides</Label>
            <Select 
              value={formData.slideCount.toString()} 
              onValueChange={(value) => updateFormData('slideCount', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select number of slides" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 slides</SelectItem>
                <SelectItem value="10">10 slides</SelectItem>
                <SelectItem value="15">15 slides</SelectItem>
                <SelectItem value="20">20 slides</SelectItem>
                <SelectItem value="25">25 slides</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Include Images Toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="includeImages"
              checked={formData.includeImages}
              onChange={(e) => updateFormData('includeImages', e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="includeImages" className="text-sm">
              Include relevant images from Unsplash
            </Label>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline">
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !formData.topic.trim()}>
              {isLoading ? 'Generating...' : 'Generate Presentation'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
