'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  ArrowDownTrayIcon,
  DocumentIcon,
  PhotoIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { Presentation, Template } from '@/types';
import { ExportService, ExportOptions } from '@/lib/export-service';
import { TemplateService } from '@/lib/templates';

interface ExportButtonProps {
  presentation: Presentation;
  template?: Template;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function ExportButton({ 
  presentation, 
  template,
  variant = 'outline',
  size = 'sm',
  className = ''
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFormat, setExportFormat] = useState<string>('');
  const [showProgress, setShowProgress] = useState(false);

  // Get template if not provided
  const activeTemplate = template || TemplateService.getTemplateById(presentation.template);

  const handleExport = async (format: 'pdf' | 'png' | 'jpg') => {
    if (!activeTemplate) {
      console.error('No template available for export');
      return;
    }

    setIsExporting(true);
    setExportFormat(format);
    setExportProgress(0);
    setShowProgress(true);

    try {
      const options: ExportOptions = {
        format,
        quality: 0.95,
        slideSize: 'widescreen',
      };

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      let blob: Blob;
      
      if (format === 'pdf') {
        blob = await ExportService.exportToPDF(presentation, activeTemplate, options);
      } else {
        // For image formats, export first slide as sample
        // In a full implementation, you might export all slides as a zip
        const firstSlide = presentation.slides[0];
        if (firstSlide) {
          blob = await ExportService.exportSlideAsImage(firstSlide, activeTemplate, options);
        } else {
          throw new Error('No slides to export');
        }
      }

      clearInterval(progressInterval);
      setExportProgress(100);

      // Generate filename and download
      const filename = ExportService.generateFileName(presentation, format);
      ExportService.downloadFile(blob, filename);

      // Show completion briefly
      setTimeout(() => {
        setShowProgress(false);
        setIsExporting(false);
        setExportProgress(0);
      }, 1000);

    } catch (error) {
      console.error('Export failed:', error);
      setShowProgress(false);
      setIsExporting(false);
      setExportProgress(0);
      // TODO: Show error toast
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <DocumentIcon className="h-4 w-4" />;
      case 'png':
      case 'jpg':
        return <PhotoIcon className="h-4 w-4" />;
      default:
        return <ArrowDownTrayIcon className="h-4 w-4" />;
    }
  };

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'pdf':
        return 'Best for sharing and printing';
      case 'png':
        return 'High quality images with transparency';
      case 'jpg':
        return 'Compressed images, smaller file size';
      default:
        return '';
    }
  };

  if (!activeTemplate) {
    return (
      <Button variant={variant} size={size} disabled className={className}>
        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
        Export
      </Button>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant={variant} 
            size={size} 
            className={className}
            disabled={isExporting}
          >
            {isExporting ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            )}
            Export
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem onClick={() => handleExport('pdf')}>
            <DocumentIcon className="h-4 w-4 mr-3" />
            <div>
              <div className="font-medium">Export as PDF</div>
              <div className="text-xs text-gray-500">Best for sharing and printing</div>
            </div>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={() => handleExport('png')}>
            <PhotoIcon className="h-4 w-4 mr-3" />
            <div>
              <div className="font-medium">Export as PNG</div>
              <div className="text-xs text-gray-500">High quality images</div>
            </div>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => handleExport('jpg')}>
            <PhotoIcon className="h-4 w-4 mr-3" />
            <div>
              <div className="font-medium">Export as JPG</div>
              <div className="text-xs text-gray-500">Smaller file size</div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Export Progress Dialog */}
      <Dialog open={showProgress} onOpenChange={setShowProgress}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {exportProgress === 100 ? (
                <CheckIcon className="h-5 w-5 text-green-500 mr-2" />
              ) : (
                <LoadingSpinner size="sm" className="mr-2" />
              )}
              {exportProgress === 100 ? 'Export Complete!' : 'Exporting Presentation'}
            </DialogTitle>
            <DialogDescription>
              {exportProgress === 100 
                ? `Your presentation has been exported as ${exportFormat.toUpperCase()}`
                : `Converting your presentation to ${exportFormat.toUpperCase()} format...`
              }
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Progress value={exportProgress} className="w-full" />
            <div className="text-center text-sm text-gray-600">
              {exportProgress}% complete
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
