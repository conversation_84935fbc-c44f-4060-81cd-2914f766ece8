'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import MainLayout from '@/components/layout/MainLayout';
import SlideRenderer from '@/components/presentation/SlideRenderer';
import ExportButton from '@/components/presentation/ExportButton';
import ImagePicker from '@/components/image/ImagePicker';
import { LoadingCard } from '@/components/ui/loading';
import { ErrorMessage } from '@/components/ui/error-boundary';
import { 
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { Presentation, Slide, Template } from '@/types';
import { ApiClient } from '@/lib/api-client';
import { TemplateService } from '@/lib/templates';
import { ImageSearchResult } from '@/lib/image-service';

export default function EditorPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useUser();
  const presentationId = params.id as string;

  const [presentation, setPresentation] = useState<Presentation | null>(null);
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [selectedSlideIndex, setSelectedSlideIndex] = useState(0);
  const [editingSlide, setEditingSlide] = useState<Slide | null>(null);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    loadPresentation();
  }, [presentationId]);

  const loadPresentation = async () => {
    try {
      setLoading(true);
      setError(null);

      // For MVP, we'll create a mock presentation if it doesn't exist
      // In production, this would fetch from the API
      if (presentationId === 'generated-123') {
        // Create a mock presentation for demo
        const mockPresentation: Presentation = {
          _id: 'generated-123',
          userId: 'user1',
          title: 'Introduction to Machine Learning',
          topic: 'A comprehensive overview of machine learning concepts for business leaders',
          template: 'modern',
          slides: [
            {
              type: 'title',
              title: 'Introduction to Machine Learning',
              content: 'A comprehensive overview of machine learning concepts for business leaders\n\nPresented by: AI Presenter\nDate: ' + new Date().toLocaleDateString(),
              order: 0,
              layout: 'default',
            },
            {
              type: 'content',
              title: 'What is Machine Learning?',
              content: '• Machine Learning is a subset of artificial intelligence\n• Enables computers to learn and improve from experience\n• Makes predictions or decisions without explicit programming\n• Powers many modern applications we use daily',
              order: 1,
              layout: 'default',
            },
            {
              type: 'content',
              title: 'Types of Machine Learning',
              content: '• **Supervised Learning**: Learning with labeled examples\n• **Unsupervised Learning**: Finding patterns in unlabeled data\n• **Reinforcement Learning**: Learning through trial and error\n• **Deep Learning**: Neural networks with multiple layers',
              order: 2,
              layout: 'default',
            },
            {
              type: 'conclusion',
              title: 'Key Takeaways',
              content: '• Machine Learning is transforming business operations\n• Start with clear use cases and quality data\n• Consider both opportunities and challenges\n• Invest in team training and development\n\n**Next Steps**: Identify pilot projects for your organization',
              order: 3,
              layout: 'default',
            },
          ],
          settings: {
            isPublic: false,
            allowComments: false,
          },
          metadata: {
            createdAt: new Date(),
            updatedAt: new Date(),
            wordCount: 150,
            slideCount: 4,
          },
        };
        
        setPresentation(mockPresentation);
        const loadedTemplate = TemplateService.getTemplateById('modern');
        setTemplate(loadedTemplate);
      } else {
        // Try to load from API
        const response = await ApiClient.getPresentation(presentationId);
        if (response.success) {
          setPresentation(response.presentation);
          const loadedTemplate = TemplateService.getTemplateById(response.presentation.template);
          setTemplate(loadedTemplate);
        } else {
          setError('Presentation not found');
        }
      }
    } catch (err) {
      console.error('Error loading presentation:', err);
      setError('Failed to load presentation');
    } finally {
      setLoading(false);
    }
  };

  const savePresentation = async () => {
    if (!presentation) return;

    try {
      setSaving(true);
      // In production, this would save to the API
      console.log('Saving presentation:', presentation);
      // await ApiClient.updatePresentation(presentation._id!, presentation);
    } catch (err) {
      console.error('Error saving presentation:', err);
    } finally {
      setSaving(false);
    }
  };

  const updateSlide = (index: number, updatedSlide: Partial<Slide>) => {
    if (!presentation) return;

    const newSlides = [...presentation.slides];
    newSlides[index] = { ...newSlides[index], ...updatedSlide };
    
    setPresentation({
      ...presentation,
      slides: newSlides,
      metadata: {
        ...presentation.metadata,
        updatedAt: new Date(),
      },
    });
  };

  const addSlide = (afterIndex?: number) => {
    if (!presentation) return;

    const newSlide: Slide = {
      type: 'content',
      title: 'New Slide',
      content: 'Add your content here...',
      order: presentation.slides.length,
      layout: 'default',
    };

    const insertIndex = afterIndex !== undefined ? afterIndex + 1 : presentation.slides.length;
    const newSlides = [...presentation.slides];
    newSlides.splice(insertIndex, 0, newSlide);

    // Update order for all slides
    newSlides.forEach((slide, index) => {
      slide.order = index;
    });

    setPresentation({
      ...presentation,
      slides: newSlides,
      metadata: {
        ...presentation.metadata,
        slideCount: newSlides.length,
        updatedAt: new Date(),
      },
    });

    setSelectedSlideIndex(insertIndex);
  };

  const deleteSlide = (index: number) => {
    if (!presentation || presentation.slides.length <= 1) return;

    const newSlides = presentation.slides.filter((_, i) => i !== index);
    
    // Update order for remaining slides
    newSlides.forEach((slide, i) => {
      slide.order = i;
    });

    setPresentation({
      ...presentation,
      slides: newSlides,
      metadata: {
        ...presentation.metadata,
        slideCount: newSlides.length,
        updatedAt: new Date(),
      },
    });

    // Adjust selected slide index
    if (selectedSlideIndex >= newSlides.length) {
      setSelectedSlideIndex(newSlides.length - 1);
    }
  };

  const moveSlide = (fromIndex: number, direction: 'up' | 'down') => {
    if (!presentation) return;

    const toIndex = direction === 'up' ? fromIndex - 1 : fromIndex + 1;
    if (toIndex < 0 || toIndex >= presentation.slides.length) return;

    const newSlides = [...presentation.slides];
    [newSlides[fromIndex], newSlides[toIndex]] = [newSlides[toIndex], newSlides[fromIndex]];
    
    // Update order
    newSlides.forEach((slide, index) => {
      slide.order = index;
    });

    setPresentation({
      ...presentation,
      slides: newSlides,
      metadata: {
        ...presentation.metadata,
        updatedAt: new Date(),
      },
    });

    setSelectedSlideIndex(toIndex);
  };

  const handleImageSelect = (image: ImageSearchResult) => {
    if (!presentation) return;

    updateSlide(selectedSlideIndex, {
      imageUrl: image.url,
      imageAlt: image.altText,
    });
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <LoadingCard title="Loading presentation..." />
        </div>
      </MainLayout>
    );
  }

  if (error || !presentation || !template) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <ErrorMessage
            title="Error Loading Presentation"
            message={error || 'Presentation not found'}
            action={{
              label: 'Back to Dashboard',
              onClick: () => router.push('/dashboard'),
            }}
          />
        </div>
      </MainLayout>
    );
  }

  const currentSlide = presentation.slides[selectedSlideIndex];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => router.push('/dashboard')}
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-xl font-semibold">{presentation.title}</h1>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Badge variant="secondary">{template.name}</Badge>
                    <span>•</span>
                    <span>{presentation.slides.length} slides</span>
                    <span>•</span>
                    <span>Last saved: {saving ? 'Saving...' : 'Just now'}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  {previewMode ? 'Edit' : 'Preview'}
                </Button>
                <ExportButton 
                  presentation={presentation}
                  template={template}
                />
                <Button size="sm" onClick={savePresentation} disabled={saving}>
                  {saving ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Slide List */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Slides</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {presentation.slides.map((slide, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded border cursor-pointer transition-colors ${
                        selectedSlideIndex === index
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedSlideIndex(index)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">
                            {index + 1}. {slide.title}
                          </div>
                          <div className="text-xs text-gray-500 capitalize">
                            {slide.type}
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              moveSlide(index, 'up');
                            }}
                            disabled={index === 0}
                            className="h-6 w-6 p-0"
                          >
                            <ChevronUpIcon className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              moveSlide(index, 'down');
                            }}
                            disabled={index === presentation.slides.length - 1}
                            className="h-6 w-6 p-0"
                          >
                            <ChevronDownIcon className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addSlide()}
                    className="w-full"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Slide
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Main Editor */}
            <div className="lg:col-span-3">
              {previewMode ? (
                /* Preview Mode */
                <Card>
                  <CardContent className="p-6">
                    <SlideRenderer
                      slide={currentSlide}
                      template={template}
                      isPreview={false}
                    />
                  </CardContent>
                </Card>
              ) : (
                /* Edit Mode */
                <div className="space-y-6">
                  {/* Slide Preview */}
                  <Card>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm">
                          Slide {selectedSlideIndex + 1} Preview
                        </CardTitle>
                        <div className="flex items-center space-x-2">
                          <ImagePicker
                            onImageSelect={handleImageSelect}
                            searchQuery={currentSlide.title}
                            trigger={
                              <Button variant="outline" size="sm">
                                <PhotoIcon className="h-4 w-4 mr-2" />
                                {currentSlide.imageUrl ? 'Change Image' : 'Add Image'}
                              </Button>
                            }
                          />
                          {presentation.slides.length > 1 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteSlide(selectedSlideIndex)}
                            >
                              <TrashIcon className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <SlideRenderer
                        slide={currentSlide}
                        template={template}
                        isPreview={true}
                      />
                    </CardContent>
                  </Card>

                  {/* Slide Editor */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center">
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit Slide Content
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Slide Title
                        </label>
                        <Input
                          value={currentSlide.title}
                          onChange={(e) => updateSlide(selectedSlideIndex, { title: e.target.value })}
                          placeholder="Enter slide title..."
                        />
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Slide Content
                        </label>
                        <Textarea
                          value={currentSlide.content}
                          onChange={(e) => updateSlide(selectedSlideIndex, { content: e.target.value })}
                          placeholder="Enter slide content..."
                          className="min-h-[200px]"
                        />
                        <div className="text-xs text-gray-500 mt-1">
                          Use • or - for bullet points. Use ## for headings.
                        </div>
                      </div>

                      {currentSlide.imageUrl && (
                        <div>
                          <label className="text-sm font-medium mb-2 block">
                            Current Image
                          </label>
                          <div className="flex items-center space-x-3">
                            <img
                              src={currentSlide.imageUrl}
                              alt={currentSlide.imageAlt || 'Slide image'}
                              className="w-20 h-15 object-cover rounded border"
                            />
                            <div className="flex-1">
                              <Input
                                value={currentSlide.imageAlt || ''}
                                onChange={(e) => updateSlide(selectedSlideIndex, { imageAlt: e.target.value })}
                                placeholder="Image description (alt text)..."
                                className="text-sm"
                              />
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateSlide(selectedSlideIndex, { imageUrl: undefined, imageAlt: undefined })}
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
