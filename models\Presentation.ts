import mongoose, { Schema, Document } from 'mongoose';
import { Presentation, Slide } from '@/types';

export interface PresentationDocument extends Presentation, Document {}

const SlideSchema = new Schema<Slide>({
  type: {
    type: String,
    enum: ['title', 'content', 'image', 'chart', 'conclusion'],
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  content: {
    type: String,
    required: true,
  },
  imageUrl: {
    type: String,
    trim: true,
  },
  imageAlt: {
    type: String,
    trim: true,
  },
  order: {
    type: Number,
    required: true,
  },
  layout: {
    type: String,
    enum: ['default', 'image-left', 'image-right', 'image-full', 'text-only'],
    default: 'default',
  },
});

const PresentationSchema = new Schema<PresentationDocument>({
  userId: {
    type: String,
    required: true,
    index: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  topic: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  slides: [SlideSchema],
  template: {
    type: String,
    required: true,
    default: 'modern',
  },
  settings: {
    isPublic: {
      type: Boolean,
      default: false,
    },
    allowComments: {
      type: Boolean,
      default: false,
    },
    shareLink: {
      type: String,
      trim: true,
    },
  },
  metadata: {
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
    generationTime: {
      type: Number,
    },
    wordCount: {
      type: Number,
      default: 0,
    },
    slideCount: {
      type: Number,
      default: 0,
    },
  },
  generationOptions: {
    audience: {
      type: String,
      enum: ['business', 'academic', 'general', 'technical'],
      default: 'general',
    },
    tone: {
      type: String,
      enum: ['professional', 'casual', 'academic', 'creative'],
      default: 'professional',
    },
    length: {
      type: Number,
      default: 10,
    },
  },
}, {
  timestamps: true,
});

// Indexes for better performance
PresentationSchema.index({ userId: 1, createdAt: -1 });
PresentationSchema.index({ 'settings.isPublic': 1 });
PresentationSchema.index({ title: 'text', topic: 'text' });

// Update metadata before saving
PresentationSchema.pre('save', function(next) {
  this.metadata.updatedAt = new Date();
  this.metadata.slideCount = this.slides.length;
  
  // Calculate word count
  let wordCount = 0;
  this.slides.forEach(slide => {
    wordCount += slide.title.split(' ').length;
    wordCount += slide.content.split(' ').length;
  });
  this.metadata.wordCount = wordCount;
  
  next();
});

export default mongoose.models.Presentation || mongoose.model<PresentationDocument>('Presentation', PresentationSchema);
