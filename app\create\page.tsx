'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import CreatePresentationForm from '@/components/forms/CreatePresentationForm';
import TemplateCard from '@/components/template/TemplateCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { LoadingCard } from '@/components/ui/loading';
import { 
  ArrowLeftIcon,
  CheckIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { GenerationRequest, Template } from '@/types';
import { TemplateService } from '@/lib/templates';

type Step = 'form' | 'template' | 'generating' | 'complete';

const mockUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  subscription: 'free' as const,
};

export default function CreatePresentationPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<Step>('form');
  const [formData, setFormData] = useState<GenerationRequest | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedPresentationId, setGeneratedPresentationId] = useState<string | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);

  useEffect(() => {
    // Load templates when component mounts
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setTemplatesLoading(true);
    try {
      // For MVP, we'll use the local template service
      // In production, this would be an API call
      const allTemplates = TemplateService.getAllTemplates();
      setTemplates(allTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
      // Fallback to default templates
      setTemplates(TemplateService.getAllTemplates());
    } finally {
      setTemplatesLoading(false);
    }
  };

  const handleFormSubmit = (data: GenerationRequest) => {
    setFormData(data);
    setCurrentStep('template');
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
  };

  const handleStartGeneration = async () => {
    if (!formData || !selectedTemplate) return;

    setCurrentStep('generating');
    
    // Simulate AI generation process
    const steps = [
      'Analyzing your topic...',
      'Creating presentation outline...',
      'Generating slide content...',
      'Finding relevant images...',
      'Applying template design...',
      'Finalizing presentation...',
    ];

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setGenerationProgress(((i + 1) / steps.length) * 100);
    }

    // Mock generated presentation ID
    setGeneratedPresentationId('generated-123');
    setCurrentStep('complete');
  };

  const handleViewPresentation = () => {
    if (generatedPresentationId) {
      router.push(`/editor/${generatedPresentationId}`);
    }
  };

  const handleBackToForm = () => {
    setCurrentStep('form');
    setSelectedTemplate(null);
  };

  const handleBackToTemplate = () => {
    setCurrentStep('template');
  };

  const renderStepIndicator = () => {
    const steps = [
      { key: 'form', label: 'Details', completed: currentStep !== 'form' },
      { key: 'template', label: 'Template', completed: currentStep === 'generating' || currentStep === 'complete' },
      { key: 'generating', label: 'Generate', completed: currentStep === 'complete' },
    ];

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <React.Fragment key={step.key}>
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                step.completed 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : currentStep === step.key
                  ? 'border-blue-600 text-blue-600'
                  : 'border-gray-300 text-gray-400'
              }`}>
                {step.completed ? (
                  <CheckIcon className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step.completed || currentStep === step.key
                  ? 'text-gray-900'
                  : 'text-gray-400'
              }`}>
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className={`w-16 h-0.5 mx-4 ${
                steps[index + 1].completed ? 'bg-blue-600' : 'bg-gray-300'
              }`} />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <MainLayout user={mockUser}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push('/dashboard')}
            className="mr-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create New Presentation</h1>
            <p className="text-gray-600 mt-1">Let AI help you create a professional presentation</p>
          </div>
        </div>

        {/* Step Indicator */}
        {renderStepIndicator()}

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {currentStep === 'form' && (
            <CreatePresentationForm 
              onSubmit={handleFormSubmit}
              isLoading={false}
            />
          )}

          {currentStep === 'template' && (
            <Card>
              <CardHeader>
                <CardTitle>Choose a Template</CardTitle>
                <CardDescription>
                  Select a design template for your presentation. You can customize it later.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {templatesLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <LoadingCard title="Loading templates..." />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    {templates.map((template) => (
                      <TemplateCard
                        key={template._id}
                        template={template}
                        onSelect={handleTemplateSelect}
                        isSelected={selectedTemplate?._id === template._id}
                      />
                    ))}
                  </div>
                )}
                
                <div className="flex justify-between">
                  <Button variant="outline" onClick={handleBackToForm}>
                    Back
                  </Button>
                  <Button 
                    onClick={handleStartGeneration}
                    disabled={!selectedTemplate}
                  >
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    Generate Presentation
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 'generating' && (
            <Card>
              <CardContent className="py-12">
                <div className="text-center">
                  <div className="mb-6">
                    <SparklesIcon className="h-12 w-12 text-blue-600 mx-auto animate-pulse" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Generating Your Presentation</h3>
                  <p className="text-gray-600 mb-6">
                    Our AI is creating your presentation based on "{formData?.topic}"
                  </p>
                  <div className="max-w-md mx-auto">
                    <Progress value={generationProgress} className="mb-4" />
                    <p className="text-sm text-gray-500">
                      {Math.round(generationProgress)}% complete
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 'complete' && (
            <Card>
              <CardContent className="py-12 text-center">
                <div className="mb-6">
                  <CheckIcon className="h-12 w-12 text-green-600 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Presentation Created Successfully!</h3>
                <p className="text-gray-600 mb-6">
                  Your presentation "{formData?.topic}" is ready for editing and customization.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button variant="outline" onClick={() => router.push('/dashboard')}>
                    Back to Dashboard
                  </Button>
                  <Button onClick={handleViewPresentation}>
                    Open in Editor
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
