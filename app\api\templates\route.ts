import { NextRequest, NextResponse } from 'next/server';
import { TemplateService } from '@/lib/templates';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'free', 'premium', or 'all'

    let templates;
    
    switch (type) {
      case 'free':
        templates = TemplateService.getFreeTemplates();
        break;
      case 'premium':
        templates = TemplateService.getPremiumTemplates();
        break;
      default:
        templates = TemplateService.getAllTemplates();
    }

    return NextResponse.json({
      success: true,
      templates,
      count: templates.length,
    });

  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch templates' 
      },
      { status: 500 }
    );
  }
}
